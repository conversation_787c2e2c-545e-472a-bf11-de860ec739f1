import axios from "axios";
import SharedCache from "../sharedCache";
import config from '../config.json';


export const getVideoKycVerificationStatus = async (userId) => {
  try {


    // Ensure config.apiUrl is set and the path matches your backend route
    const apiEndpoint = `${config.apiUrl}/ops/invoiceFinancing/videoKycStatus`; // ADJUST PATH IF NEEDED

    const response = await fetch(apiEndpoint, {
      method: 'POST', // Use GET method
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ userId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `Request failed with status: ${response.status}`);
    }

    if (data.success) {
      // Return the specific statuses fetched
      return {
        success: true,
        videoKycStatus: data.videoKycStatus,
        overallKycStatus: data.overallKycStatus,
        declineReason: data.declineReason
      };
    } else {
      // Handle cases where backend returns success: false
      throw new Error(data.error || "Failed to fetch KYC status from server.");
    }

  } catch (error) {
    console.error("Error fetching video KYC status:", error);
    return { success: false, error: error.message || "Failed to retrieve Video KYC status." };
  }
};

export const initiateVideoKyc = async (userId, journeyId = null) => {
  try {
    // Retrieve auth token using your app's method

    // Construct the correct API endpoint URL
    // Ensure config.apiUrl points to the base of your API (e.g., 'https://your-api-domain.com')
    // Ensure the path matches where you mounted the route (e.g., '/api/kyc/initiateVideoKycJourney')
    const apiEndpoint = `${config.apiUrl}/ops/invoiceFinancing/initiateVideoKycJourney`; // ADJUST PATH IF NEEDED

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      // Send userId if your backend needs it in the body,
      // otherwise rely on backend extracting it from the token
      body: JSON.stringify({ userId, journeyId }),
    });

    const data = await response.json();

    if (!response.ok) {
      // Use error message from backend if available, otherwise default
      throw new Error(data.error || `Request failed with status: ${response.status}`);
    }

    // Check if backend explicitly signaled success and provided the URL
    if (data.success && data.verificationUrl) {
      return data; // Contains { success: true, verificationUrl: '...', reference: '...' }
    } else {
      // Handle cases where backend might return 200 OK but didn't succeed logically
      throw new Error(data.error || data.message || "Initiation succeeded but no verification URL received.");
    }

  } catch (error) {
    console.error("Error initiating video KYC:", error);
    // Return a consistent error structure
    return { success: false, error: error.message || "Failed to initiate Video KYC process." };
  }
};

// --- Get KYC information for a user ---
export const getKycInfo = async (userId) => {
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("getKycInfo: No token found in SharedCache.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (!userId) {
      console.error("getKycInfo: No userId provided.");
      return { success: false, message: "User ID is required." };
    }

    console.log(`getKycInfo: Fetching for userId: ${userId}`);
    const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/getKyc/${userId}`, {
      headers: {
        "x-auth-token": token
      }
    });

    const kycData = response.data;
    console.log("getKycInfo: API Response Data:", kycData);

    // Update user's KYC info in SharedCache - Optional, depends on your app's flow
    if (kycData.success && kycData.user) { // Check if user data is present
      const user = SharedCache.get("user");
      if (user) {
        // Merge existing user data with potentially updated fields from response
        const updatedUser = {
          ...user, // Keep existing fields
          ...kycData.user, // Overwrite with fields from response (like firstName, kyc object etc.)
          // Ensure the nested kyc object is merged if needed, or just replaced
          kyc: kycData.user.kyc || user.kyc, // Prefer fresh kyc data if available
        };
        SharedCache.set("user", updatedUser);
        console.log("getKycInfo: Updated user in SharedCache.");
      }
    }

    return kycData;
  } catch (error) {
    console.error("Error fetching KYC data:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to fetch KYC information"
    };
  }
};

// --- Update KYC information (Main details, Buyers, Directors) ---
export const updateKyc = async (kycData) => {
  console.log(kycData, "Adasd")
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("updateKyc: No token found in SharedCache.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (!kycData || !kycData.id) {
      console.error("updateKyc: Invalid payload. 'id' is required.", kycData);
      return { success: false, message: "Invalid data: User ID is missing." };
    }
 
    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/updateKyc`,
      kycData, // Send the constructed payload directly
      {
        headers: {
          "x-auth-token": token,
          "Content-Type": "application/json", // Ensure correct content type
        },
      }
    );

    console.log("updateKyc: API response data:", response.data);
    const returnedData = response.data;

    // Update cache if successful and user data is returned
    if (returnedData.success && returnedData.user) {
      const user = SharedCache.get("user");
      if (user) {
        const updatedUser = {
          ...user,
          ...returnedData.user, // Update with response data
          kyc: returnedData.user.kyc || user.kyc, // Merge/replace kyc
        };
        SharedCache.set("user", updatedUser);
        console.log("updateKyc: Updated user in SharedCache.");
      }
    }

    return returnedData;
  } catch (error) {
    console.error("Error updating KYC:", error);
    // Log detailed error information
    if (error.response) {
      console.error("API error response data:", error.response.data);
      console.error("API error response status:", error.response.status);
    } else if (error.request) {
      console.error("No response received. Request made:", error.request);
    } else {
      console.error("Error setting up the request:", error.message);
    }

    return {
      success: false,
      message: error.response?.data?.message || "Failed to update KYC information",
    };
  }
};

export const updateBuyers = async (payload) => {
  console.log("updateBuyers called with (payload):", JSON.stringify(payload, null, 2));
  // Payload structure expected: { userId: string, buyers: Array<object> }
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("updateBuyers: No token found.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (!payload || !payload.userId || !Array.isArray(payload.buyers)) {
      console.error("updateBuyers: Invalid payload.", payload);
      return { success: false, message: "Invalid data: userId and buyers array required." };
    }

    // Optional: Validate buyer structure within the payload before sending if needed
    // payload.buyers.forEach((buyer, index) => {
    //     if (!buyer.buyerName?.trim()) {
    //         throw new Error(`Buyer Name is required for buyer entry ${index + 1}`);
    //     }
    // });


    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/kyc/buyers/update`, // Verify this endpoint matches your backend router
      payload,
      {
        headers: {
          "x-auth-token": token,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("updateBuyers: API response data:", response.data);
    // Return the response directly. The calling component might use the returned buyers array.
    return response.data; // Should contain { success: true, message: '...', buyers: [...] }

  } catch (error) {
    console.error("Error updating Buyer data:", error);
    // Log detailed error information
    if (error.response) {
      console.error("API error response data:", error.response.data);
      console.error("API error response status:", error.response.status);
    } else if (error.request) {
      console.error("No response received. Request made:", error.request);
    } else {
      console.error("Error setting up the request:", error.message);
    }

    // Return a generic error structure
    return {
      success: false,
      message: error.response?.data?.message || "Failed to update buyer information",
      // Optionally include specific validation errors if the backend provides them
      errors: error.response?.data?.errors
    };
  }
};

// --- Update Shareholder Text Data ---
export const updateShareholderTextData = async (payload) => {
  console.log("updateShareholderTextData called with (payload):", JSON.stringify(payload, null, 2));
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("updateShareholderTextData: No token found.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (!payload || !payload.userId || !Array.isArray(payload.shareholders)) {
      console.error("updateShareholderTextData: Invalid payload.", payload);
      return { success: false, message: "Invalid data: userId and shareholders array required." };
    }

    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/shareholders/updateTextData`,
      payload,
      {
        headers: {
          "x-auth-token": token,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("updateShareholderTextData: API response data:", response.data);
    // No explicit cache update here, assuming main save/fetch handles it
    return response.data;

  } catch (error) {
    console.error("Error updating Shareholder text data:", error);
    if (error.response) {
      console.error("API error response data:", error.response.data);
    }
    return {
      success: false,
      message: error.response?.data?.message || "Failed to update shareholder information",
    };
  }
};


// --- Upload/Replace a non-shareholder KYC document ---
// Refactored to accept individual parameters and construct FormData internally
export const uploadKycDocument = async (userId, documentType, file) => {
  console.log(`uploadKycDocument called: userId=${userId}, docType=${documentType}, file=${file?.name}`);
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("uploadKycDocument: No token found.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (!userId || !documentType || !file) {
      console.error("uploadKycDocument: Missing required parameters.");
      return { success: false, message: "User ID, document type, and file are required." };
    }

    // Construct FormData
    const formData = new FormData();
    formData.append('userId', userId); // Or 'id' depending on backend expectation, check route
    formData.append('documentType', documentType);
    formData.append('file', file);

    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/uploadKycDocument`,
      formData, // Send the constructed FormData
      {
        headers: {
          "x-auth-token": token,
          "Content-Type": "multipart/form-data", // Axios sets this correctly with FormData, but doesn't hurt to be explicit
        }
      }
    );

    console.log("uploadKycDocument: API response data:", response.data);
    const returnedData = response.data;

    // Optional: Update cache if needed, though usually handled after a full save/fetch
    // if (returnedData.success && returnedData.kyc) { // Assuming response might contain updated kyc
    //   const user = SharedCache.get("user");
    //   if (user) {
    //     const updatedUser = { ...user, kyc: returnedData.kyc };
    //     SharedCache.set("user", updatedUser);
    //   }
    // }

    // IMPORTANT: The component expects `filePath` and `signedUrl` in the response.
    // Ensure the backend '/uploadKycDocument' provides these for immediate UI update.
    // If backend only returns success=true, the component needs adjustment or a follow-up fetch.
    // Assuming backend returns { success: true, filePath: '...', signedUrl: '...' }
    // Let's modify the return to match component expectation structure slightly
    if (returnedData.success) {
      return {
        success: true,
        message: returnedData.message || "Document uploaded successfully",
        // Mimic the structure expected by handleFileReplace's success path
        documentData: {
          filePath: returnedData.filePath,
          signedUrl: returnedData.signedUrl,
          uploadedOn: new Date().toISOString(), // Add timestamp if backend doesn't
          mimeType: file.type, // Add mimeType if backend doesn't
        }
      };
    } else {
      return returnedData; // Return the original error structure
    }

  } catch (error) {
    console.error(`Error uploading document (${documentType}):`, error);
    if (error.response) {
      console.error("API error response data:", error.response.data);
    }
    return {
      success: false,
      message: error.response?.data?.message || `Failed to upload ${documentType}`,
      // Ensure a structure component might check
      error: error.response?.data?.error || `Failed to upload ${documentType}`
    };
  }
};


// --- Upload/Replace a specific shareholder document ---
export const uploadShareholderDocument = async (userId, shareholderIndex, shareholderDocKey, file) => {
  console.log(`uploadShareholderDocument called: userId=${userId}, index=${shareholderIndex}, key=${shareholderDocKey}, file=${file?.name}`);
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) {
    //   console.error("uploadShareholderDocument: No token found.");
    //   return { success: false, message: "Authentication token not found." };
    // }
    if (userId === undefined || userId === null || shareholderIndex === undefined || shareholderIndex === null || !shareholderDocKey || !file) {
      console.error("uploadShareholderDocument: Missing required parameters.");
      return { success: false, message: "User ID, shareholder index, document key, and file are required." };
    }


    // Construct FormData
    const formData = new FormData();
    formData.append('userId', userId);
    formData.append('shareholderIndex', shareholderIndex);
    formData.append('shareholderDocKey', shareholderDocKey); // e.g., 'passport', 'qid'
    formData.append('file', file);

    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/shareholders/uploadDoc`, // Endpoint for shareholder docs
      formData,
      {
        headers: {
          "x-auth-token": token,
          "Content-Type": "multipart/form-data",
        }
      }
    );

    console.log("uploadShareholderDocument: API response data:", response.data);
    // Assuming backend returns { success: true, documentData: { filePath, signedUrl, ... } }
    // Return the structure expected by the calling component
    if (response.data?.success && response.data?.documentData) {
      return {
        success: true,
        message: response.data.message || "Shareholder document uploaded successfully",
        documentData: response.data.documentData // Pass the nested document data object
      };
    } else {
      // Return the original response if structure is different or error occurred
      return response.data;
    }

  } catch (error) {
    console.error(`Error uploading shareholder document (${shareholderDocKey}, index ${shareholderIndex}):`, error);
    if (error.response) {
      console.error("API error response data:", error.response.data);
    }
    return {
      success: false,
      message: error.response?.data?.message || `Failed to upload shareholder ${shareholderDocKey}`,
      error: error.response?.data?.error || `Failed to upload shareholder ${shareholderDocKey}`
    };
  }
};


// --- Upload Additional Invoice Document (Existing) ---
export const uploadAdditionalInvoiceDocument = async (invoiceId, file) => {
  const token = SharedCache.get("token") || "placeholdertoken";
  const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;

  // if (!token) return { success: false, message: "Authentication token not found." };
  if (!userId || !invoiceId || !file) {
    console.error("uploadAdditionalInvoiceDocument: Missing required parameters.");
    return { success: false, message: "Invoice ID, User ID and file are required." };
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId);

  try {
    const response = await axios.post(
      `${config.apiUrl}/ops/invoiceFinancing/invoices/${invoiceId}/uploadAdditionalDocument`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
          'x-auth-token': token,
        },
      }
    );
    console.log("uploadAdditionalInvoiceDocument: Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error uploading additional invoice document:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to upload additional document."
    };
  }
};


// --- Check KYC status (Existing) ---
export const checkKycStatus = async (userId) => {
  try {
    const token = SharedCache.get("token") || "placeholdertoken";
    // if (!token) return { success: false, message: "Authentication token not found.", isComplete: false };
    if (!userId) return { success: false, message: "User ID required.", isComplete: false };


    const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/checkKycStatus/${userId}`, {
      headers: {
        "x-auth-token": token
      }
    });

    const returnedData = response.data;

    // Optional Cache update
    // if (returnedData.success && returnedData.kyc) {
    //   const user = SharedCache.get("user");
    //   if (user) {
    //     const updatedUser = { ...user, kyc: returnedData.kyc };
    //     SharedCache.set("user", updatedUser);
    //   }
    // }

    return returnedData; // Backend should return { success: true/false, isComplete: true/false, message: ..., kyc?: ... }
  } catch (error) {
    console.error("Error checking KYC status:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to check KYC status",
      isComplete: false // Assume incomplete on error
    };
  }
};