import React, { useState, useEffect, useRef } from "react";
import { useHistory } from "react-router-dom";
import {
  fetchOtpForLogin,
  validateOtpForLoginAndCheckKYC,
  emailLogin,
  emailSignup,
  fakeOtpSend,
} from "../api/auth";
import { GoogleLogin as GL } from '@react-oauth/google';
import SharedCache from "../sharedCache";
import http from '../services/common/httpService';
import config from '../config.json';
import {
  countries,
  getCountryFlagEmojiFromCountryCode,
  getCountryFromCountryCode,
} from 'country-codes-flags-phone-codes';
import backdrop from "../images/backdrop.png";
import { useUser } from "../contexts/UserContext";
import { logout } from "../api/auth";
import { decryptData } from "../utils/Masking";

async function populateAssociatedRoleData() {
  const activeUser = SharedCache.get("user");
  console.log("POPULATING");
  if (!activeUser || !activeUser.email) { // Check for token here
    console.warn("populateAssociatedRoleData: Active user email or token is missing. Skipping.");
    SharedCache.set("msmeUser", null);
    SharedCache.set("buyerFullData", null);
    return;
  }
  console.log("POPULATING 2");

  const emailToCheck = activeUser.email;
  console.log(`Populating associated role data for email: ${emailToCheck}`);

  try {
    const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/check-user-roles?email=${encodeURIComponent(emailToCheck)}`);
    console.log("POPULATING", response);
    if (!response.ok) {
      console.error(`populateAssociatedRoleData: API call failed (status ${response.status}). Clearing role caches.`);
      SharedCache.set("msmeUser", null);
      SharedCache.set("buyerFullData", null);
      return;
    }
    console.log("POPULATING 5");

    // This is your NEW data handling block
    const data = await response.json();

    if (data.success && data.encryptedData) {
      const decryptedPayload = decryptData(data.encryptedData);

      if (decryptedPayload) {
        SharedCache.set("msmeUser", decryptedPayload.msmeProfile || null);
        SharedCache.set("buyerFullData", decryptedPayload.buyerAdminProfile || null);
        console.log("DAATAAAAA", decryptedPayload);
        console.log("Dual Role Populate: msmeUser cache:", decryptedPayload.msmeProfile ? 'Exists' : 'Null', "| buyerFullData cache:", decryptedPayload.buyerAdminProfile ? 'Exists' : 'Null');
      } else {
        console.error("populateAssociatedRoleData: Failed to decrypt user role data.");
        SharedCache.set("msmeUser", null);
        SharedCache.set("buyerFullData", null);
      }
    } else {
      console.error("populateAssociatedRoleData: API reported failure:", data.message, data);
      SharedCache.set("msmeUser", null);
      SharedCache.set("buyerFullData", null);
    }
  } catch (error) {
    console.error("populateAssociatedRoleData: Exception during API call:", error);
    SharedCache.set("msmeUser", null);
    SharedCache.set("buyerFullData", null);
  }
}

const LoginPage = () => {
  const [loginMethod, setLoginMethod] = useState("mobile"); // "mobile" or "email"
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isEmailVerificationSent, setIsEmailVerificationSent] = useState(false);
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [timer, setTimer] = useState(119); // 2 minutes countdown timer
  const [otpError, setOtpError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [isSendingOtp, setIsSendingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const history = useHistory();
  const { userType, setUserType } = useUser();
  const otpInputRef = useRef(null);
  const containerRef = useRef(null);
  const [index, setIndex] = useState(0);

  // Navigation handlers for Terms and Privacy Policy
  const handleTermsClick = () => {
    history.push('/terms-and-conditions');
  };

  const handlePrivacyClick = () => {
    history.push('/privacy-policy');
  };
  const allowedCountries = ["IN", "SA", "AE", "QA"];
  const [selectedCountry, setSelectedCountry] = useState({
    code: 'QA',
    name: 'Qatar',
    dialCode: '+974',
    flag: getCountryFlagEmojiFromCountryCode('QA'),
  });
  const [isOtpInputFocused, setIsOtpInputFocused] = useState(false);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev + 1) % messages.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleCountrySelect = (ctry) => {
    setSelectedCountry(ctry);
    setIsDropdownOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isDropdownOpen) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const handleLoginSuccess = async (profileData, loggedInAsType) => {
    console.log(`LoginPage.handleLoginSuccess: Primary login as ${loggedInAsType}. Profile email: ${profileData?.email}`);

    await populateAssociatedRoleData();
    setUserType(loggedInAsType); // from useUser() hook
    console.log(`LoginPage.handleLoginSuccess: Context setUserType called with '${loggedInAsType}'.`);

    if (loggedInAsType === 'buyer') {
      history.push("/buyer-dashboard");
    } else if (loggedInAsType === 'msme') {
      const userForRedirect = SharedCache.get("user"); // Should be MSME profile

      const emailMissing = !profileData?.email && !userForRedirect?.email;
      const mobileMissing = !profileData?.mobileNo && !userForRedirect?.mobileNo;
      const kycInitiated = profileData?.kyc?.verificationStatus === 'INITIATED' || userForRedirect?.kyc?.verificationStatus === 'INITIATED';

      const isNewUser = kycInitiated && (emailMissing || mobileMissing);

      console.log(profileData, "PROFILEDATA HERE");

      if (isNewUser) {
        history.push({
          pathname: "/onboarding",
          state: {
            phone: userForRedirect?.mobileNo ?? profileData?.mobileNo,
            email: userForRedirect?.email ?? profileData?.email,
            firstName: userForRedirect?.firstName ?? profileData?.firstName,
            lastName: userForRedirect?.lastName ?? profileData?.lastName
          }
        });
      } else if (userForRedirect && (
        userForRedirect.kyc?.verificationStatus === "APPROVED" ||
        userForRedirect.kyc?.verificationStatus === "UNDER_REVIEW"
      )) {
        history.push("/dashboard");
      } else {
        history.push("/eligibility-checker");
      }
    } else {
      history.push("/dashboard");
    }
  };

  const responseMessage = async (credentialResponse) => {
    setIsLoggingIn(true); setLoginError("");
    try {
      const backendGoogleLoginUrl = `${config.apiUrl}/ops/invoiceFinancing/auth/google/login`;
      const backendResponse = await fetch(backendGoogleLoginUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tokenId: credentialResponse.credential }),
      });
      // This is the NEW code block
      const data = await backendResponse.json();

      if (data.success && data.encryptedData) {
        const decryptedPayload = decryptData(data.encryptedData);

        if (decryptedPayload && decryptedPayload.user && decryptedPayload.token) {
          const { user, token } = decryptedPayload;

          SharedCache.set("user", user);
          SharedCache.set("msmeUser", user);
          SharedCache.set("buyerFullData", null);
          SharedCache.set("token", token);

          await handleLoginSuccess(user, 'msme');
        } else {
          setLoginError("Failed to process login data. Please try again.");
          logout();
        }
      } else {
        let errMsg = data.message || "Google authentication failed.";
        if (data.authMethod && data.user?.email) {
          errMsg = `This email (${data.user.email}) is already associated with an MSME account using ${data.authMethod}. Please use that login method.`;
        }
        setLoginError(errMsg);
        logout();
      }
    } catch (error) {
      console.error('Google Login: Critical fetch/network error -', error);
      setLoginError("A network or unexpected error occurred during Google authentication. Please try again.");
      logout();
    } finally {
      setIsLoggingIn(false);
    }
  };

  const errorMessage = (error) => {
    console.log(error);
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Password validation function
  const validatePassword = (password) => {
    const validations = {
      length: password.length >= 12,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      specialChar: /[!@#$%^&*(),.?&quot;:{}|&lt;&gt;]/.test(password)
    };
    setPasswordValidation(validations);
    return Object.values(validations).every(Boolean);
  };

  const handleSendOtpClick = async () => {
    console.log("ch1");
    if (!phone.length > 7) {
      setPhoneError("Enter a valid phone number (10 digits)");
      return;
    }
    console.log("ch2");

    if (isOtpSent || isSendingOtp) {
      return;
    }
    console.log("ch3");

    setIsSendingOtp(true);
    setLoginError("");

    const fullPhone = selectedCountry.dialCode + phone;

    try {
      const response = await fetchOtpForLogin(fullPhone, isSignUp);

      // Fallback to fake OTP if actual sending fails
      if (!response.success && response.fallback) {
        await fakeOtpSend(fullPhone);
      }

      if (response.success || response.fallback) {
        setIsOtpSent(true);
        setTimer(119);
        setPhoneError("");
        console.log("OTP sent. (Fallback:", !!response.fallback + ")");
      } else {
        // Check if the error is related to an existing account with a different auth method
        if (response.authMethod === 'google') {
          setLoginError(`This phone number is already associated with an existing account. Please sign in with Google.`);
        } else if (response.authMethod === 'email') {
          setLoginError(`This phone number is already associated with an existing account.`);
        } else if (response.authMethod === 'mobile') {
          setLoginError("This phone number is already registered. Please sign in instead of signing up.");
        } else {
          setLoginError(
            response.message || "There was an error while sending the OTP. Please try again later."
          );
        }
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      setLoginError("There was an error while sending the OTP. Please try again later.");
    } finally {
      setIsSendingOtp(false);
    }
  };

  const handleVerifyOtpClick = async () => {
    if (!otp) { setOtpError("Please enter OTP."); return; }
    setIsVerifyingOtp(true);
    const fullPhone = selectedCountry.dialCode + phone;
    const phoneWithoutPlus = fullPhone.replace(/^\+/, "");

    try {
      const otpResponse = await validateOtpForLoginAndCheckKYC(phoneWithoutPlus, otp);
      // ^ This function from auth.js needs to set primary SharedCache items as listed above.

      if (otpResponse.success && otpResponse.user) {
        SharedCache.set("msmeUser", otpResponse.user); // Assuming OTP login is for MSME
        SharedCache.set("buyerFullData", null); // Initialize other role's specific cache
        // SharedCache.set("token", otpResponse.tokenFromAuth); // Handled by handleLoginSuccess now
        await handleLoginSuccess(otpResponse.user, 'msme'); // Pass token
      } else {
        if (otpResponse.authMethod === 'google') { setOtpError(otpResponse.message || "Invalid OTP."); }
        else if (otpResponse.authMethod === 'email') { setOtpError(otpResponse.message || "Invalid OTP."); }
        else { setOtpError(otpResponse.message || "Invalid OTP."); }
      }
    } catch (error) {
      console.error("Error validating OTP:", error);
      setOtpError("An error occurred. Please try again.");
    } finally {
      setIsVerifyingOtp(false);
      setOtp("");
      if (otpInputRef.current) otpInputRef.current.focus();
    }
  };

  const handleEmailLogin = async () => {
    if (!email) { setEmailError("Please enter your email."); return; }
    if (!password) { setEmailError("Please enter your password."); return; } // Corrected error message

    setIsLoggingIn(true);
    setLoginError("");

    try {
      const loginResponse = await emailLogin(email, password); // From auth.js
      console.log(loginResponse, "oajdoakdo");
      // Your updated emailLogin already sets:
      // - SharedCache.user (to primary logged-in profile)
      // - SharedCache.token
      // - SharedCache.msmeUser OR SharedCache.buyerFullData (and nulls the other)
      // - Calls setUserTypeGlobally(type) which sets SharedCache.userType

      if (loginResponse.success && loginResponse.user && loginResponse.type) {
        // Assuming emailLogin in auth.js already sets SharedCache.msmeUser/buyerFullData correctly
        // and SharedCache.user, and SharedCache.token.
        // If not, you'd add:
        // SharedCache.set("msmeUser", loginResponse.type === 'msme' ? loginResponse.user : null);
        // SharedCache.set("buyerFullData", loginResponse.type === 'buyer' ? loginResponse.user : null);
        await handleLoginSuccess(loginResponse.user, loginResponse.type);
      } else {
        setLoginError(loginResponse.message || "Login failed. Please check your credentials.");
        // No logout() usually, as primary login failed.
      }
    } catch (error) {
      console.error("LoginPage: Error during email login process:", error);
      setLoginError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoggingIn(false);
      setPassword("");
      // setOtp(""); // Not relevant for email login unless you want to clear all inputs
    }
  };

  const handleEmailSignUp = async () => {
    if (!email) {
      setEmailError("Please enter your email.");
      return;
    }
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    if (!password) {
      setPasswordError("Please enter your password.");
      return;
    }
    if (!validatePassword(password)) {
      setPasswordError("Password does not meet the security requirements.");
      return;
    }
    if (!confirmPassword) {
      setConfirmPasswordError("Please confirm your password.");
      return;
    }
    if (password !== confirmPassword) {
      setConfirmPasswordError("Passwords do not match.");
      return;
    }

    setIsLoggingIn(true);

    try {
      const response = await emailSignup(email, password, confirmPassword);

      if (response.success) {
        const user = response.user || SharedCache.get("user");
        setIsEmailVerificationSent(true);
        console.log("Sign-up successful, user:", user?._id);
      } else {
        // Check if the error is related to an existing account with a different auth method
        if (response.authMethod === 'google') {
          setEmailError("This email is already associated with an existing account.");
        } else {
          setEmailError(
            response.message || "Sign-up failed. Please check your credentials."
          );
        }
      }
    } catch (error) {
      console.error("Error during email sign-up:", error);
      setEmailError("An error occurred during sign-up. Please try again.");
    } finally {
      setIsLoggingIn(false);
      setPassword("");
      setConfirmPassword("");
    }
  };

  // Handle phone input - only allow numeric input
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters
    if (value.length <= 10) {
      console.log(value);
      setPhone(value);
      if (phoneError && value.length === 10) {
        setPhoneError(""); // Clear error when valid
      }
    }
  };

  // Handle OTP input - only allow numeric input
  const handleOtpChange = (e) => {
    const value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters
    if (value.length <= 6) {
      setOtp(value);
      if (otpError && value.length === 6) {
        setOtpError(""); // Clear error when valid
      }
    }
  };

  // Handle email input
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    if (emailError) {
      setEmailError("");
    }
  };

  // Handle password input
  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    validatePassword(newPassword);
    if (passwordError) {
      setPasswordError("");
    }
  };

  // Handle confirm password input
  const handleConfirmPasswordChange = (e) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);
    if (newConfirmPassword !== password) {
      setConfirmPasswordError("Passwords do not match");
    } else {
      setConfirmPasswordError("");
    }
  };

  // Reset state to allow requesting a new OTP
  const handleResetOtp = () => {
    setIsOtpSent(false);
    setOtp("");
    setOtpError("");
    setLoginError("");
  };

  // Switch login method
  const toggleLoginMethod = () => {
    // Reset all fields and errors
    setLoginMethod(loginMethod === "mobile" ? "email" : "mobile");
    setIsOtpSent(false);
    setPhone("");
    setOtp("");
    setEmail("");
    setPassword("");
    setConfirmPassword("");
    setPhoneError("");
    setOtpError("");
    setEmailError("");
    setPasswordError("");
    setConfirmPasswordError("");
    setLoginError("");
  };

  // Timer countdown effect
  useEffect(() => {
    let interval;
    if (isOtpSent && timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else if (timer === 0) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [isOtpSent, timer]);

  // Adjust container margins to prevent overflow
  const adjustMargins = () => {
    const container = containerRef.current;
    if (container?.scrollHeight > container?.clientHeight) {
      const children = container.children;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        const style = window.getComputedStyle(child);
        const marginTop = parseFloat(style.marginTop);
        const marginBottom = parseFloat(style.marginBottom);
        const newMarginTop = Math.max(marginTop - 2, 0);
        const newMarginBottom = Math.max(marginBottom - 2, 0);
        child.style.marginTop = `${newMarginTop}px`;
        child.style.marginBottom = `${newMarginBottom}px`;
      }
    }
  };

  // Set up resize listener for margin adjustment
  useEffect(() => {
    adjustMargins();
    window.addEventListener("resize", adjustMargins);
    return () => {
      window.removeEventListener("resize", adjustMargins);
    };
  }, [
    loginMethod,
    isOtpSent,
    phone,
    otp,
    email,
    password,
    confirmPassword,
    otpError,
    phoneError,
    emailError,
    passwordError,
    confirmPasswordError,
    loginError,
  ]);

  // Format timer as MM:SS
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Render OTP verification screen

  const GoogleLogin = <div className="flex justify-center w-full"><GL onSuccess={responseMessage} onError={errorMessage} /></div>;

  const renderOtpVerificationScreen = () => {
    // Assuming 'phone', 'selectedCountry', 'handleResetOtp', 'otp', 'handleOtpChange',
    // 'otpInputRef', 'otpError', 'isVerifyingOtp', 'handleVerifyOtpClick',
    // 'timer', and 'formatTime' are defined and passed as props or are accessible in scope
    // from the parent LoginPage component.

    const PencilIcon = () => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
        className="w-4 h-4 inline-block ml-1"
      >
        <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
      </svg>
    );

    return (
      // This component is rendered within the max-w-md container of the left panel
      <div className="flex flex-col items-center w-full">
        <div className="flex flex-col items-center w-full"> {/* Consistent inner container */}
          <img
            loading="lazy"
            src={require("../images/logo.jpg")} // Ensure this path is correct
            className="w-32 md:w-36 mb-6 sm:mb-8" // Consistent logo styling
            alt="Madad Fintech Logo"
          />
          <div className="text-2xl sm:text-3xl font-bold text-gray-800 mb-8 sm:mb-10 text-center">
            Confirm your phone number
          </div>

          {/* Content Card with bg-gray-50 */}
          <div className="w-full bg-gray-50 p-6 sm:p-8 rounded-xl shadow-lg space-y-6">
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Enter the 6-digit code we just sent to
              </p>
              <div className="flex items-center justify-center mt-1">
                <p className="text-base font-medium text-gray-800">
                  {phone && selectedCountry ? `${selectedCountry.dialCode} ${phone.slice(0, 2)}****${phone.slice(-2)}` : 'your phone number'}
                </p>
                <button
                  onClick={handleResetOtp}
                  className="ml-2 text-sm text-green-600 hover:text-green-700 font-medium flex items-center"
                >
                  <PencilIcon />
                  Change
                </button>
              </div>
            </div>

            {/* Actual hidden input field */}
            <input
              type="tel"
              value={otp}
              onChange={handleOtpChange}
              maxLength={6}
              ref={otpInputRef} // Use the ref from LoginPage
              className="absolute -left-[9999px] opacity-0 w-px h-px" // Positioned off-screen, focusable
              autoFocus // Automatically focus when this screen renders
              onFocus={() => setIsOtpInputFocused(true)} // Set focus state to true
              onBlur={() => setIsOtpInputFocused(false)}  // Set focus state to false
            />

            {/* Visual display boxes for OTP */}
            <div
              className="flex justify-center space-x-2 sm:space-x-3 cursor-text" // cursor-text to indicate it's an input area
              onClick={() => otpInputRef.current?.focus()} // Focus the hidden input on click
            >
              {[0, 1, 2, 3, 4, 5].map((index) => (
                <div
                  key={index}
                  className={`w-10 h-12 sm:w-12 sm:h-14 flex items-center justify-center text-xl sm:text-2xl font-medium border rounded-md bg-white transition-colors duration-150
                    ${isOtpInputFocused && (index === otp.length || (otp.length === 6 && index === 5))
                      ? 'border-green-500 ring-1 ring-green-500' // Refined focus style: thicker border, subtle ring
                      : 'border-gray-300' // Default style
                    }
                    ${otp[index] ? 'text-gray-900' : 'text-gray-400'} {/* Text color based on content */}
                  `}
                >
                  {otp[index] || ''}
                </div>
              ))}
            </div>

            {otpError && (
              <div className="text-red-500 text-sm text-center -mt-3 sm:-mt-4">
                {otpError}
              </div>
            )}

            <button
              className={`w-full py-3 text-base sm:text-lg font-medium rounded-md transition-all duration-200 ${otp.length === 6 && !isVerifyingOtp
                ? "text-white bg-[#004141] hover:bg-[#003535] shadow-md"
                : "text-gray-500 bg-gray-200 cursor-not-allowed"
                }`}
              onClick={otp.length === 6 && !isVerifyingOtp ? handleVerifyOtpClick : undefined}
              disabled={!(otp.length === 6 && !isVerifyingOtp)}
            >
              Verify
            </button>

            <div className="text-center text-sm text-gray-500">
              Wait {formatTime(timer)} before requesting new code
            </div>
          </div>

          {/* "or try logging using email address" link */}
          <div className="mt-6 text-center text-sm text-gray-500">
            or try logging using <span className="font-medium text-gray-700 hover:underline cursor-pointer">email address</span>
            {/* If this span should be interactive, you'll need to add an onClick handler here */}
          </div>

          {/* The main LoginPage component handles the overall footer, so it's not duplicated here */}
        </div>
      </div>
    );
  };

  // Still inside your LoginPage component

  const renderEmailVerificationScreen = () => {
    return (
      <div
        ref={containerRef}
        className="flex flex-col items-center px-8 py-10 max-w-full bg-white rounded-md w-[500px] max-md:px-6 h-auto shadow-lg mx-4 my-20"
      >
        <div className="flex flex-col items-center w-full max-w-[374px]">
          <img
            loading="lazy"
            src={require("../images/logo.jpg")}
            className="w-[150px] mb-4"
            alt="Madad Fintech Logo"
          />

          <div className="text-2xl font-semibold text-[#004141] mb-8 text-center">
            Signup On Madad
          </div>

          <div className="w-full space-y-6">
            <div className="text-center text-gray-700">
              Verification link has been sent to <span className="text-[#208039]">{email}</span>
              <br />
              Please check your email and click the link to proceed with onboarding.
            </div>
            <div className="text-center text-base">
              <span
                className="text-[#208039] font-medium hover:underline cursor-pointer"
                onClick={() => setIsEmailVerificationSent(false)}
              >
                Re-enter email
              </span>
            </div>
          </div>
        </div>

        <div className="w-full mt-8 text-sm text-center text-gray-500 space-y-1">
          <p>Your data is 100% safe with us</p>
          <p>© Madad Financial Technologies. All rights reserved</p>
        </div>
      </div>
    );
  };

  const messages = [
    {
      title: 'Get Cash Today, Against Due Invoices',
      text: 'Say goodbye to delays - discount your unpaid invoices for instant cash',
    },
    {
      title: 'Boost Your Business Cashflow',
      text: 'Convert invoices into working capital in days, not weeks.',
    },
    {
      title: 'Flexible Invoice Financing',
      text: 'Choose which invoices to finance and when - complete control',
    },
  ];

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      {/* Left Panel (Form) */}
      <div className="w-full md:w-1/2 flex justify-center items-center p-6 sm:p-8 order-2 md:order-1">
        <div ref={containerRef} className="w-full max-w-md">
          {isOtpSent ? (
            renderOtpVerificationScreen()
          ) : isEmailVerificationSent ? (
            renderEmailVerificationScreen()
          ) : (
            // Main Login/Signup Form
            <div className="flex flex-col items-center w-full">
              <div className="flex flex-col items-center w-full max-w-[374px]">
                {/* Logo */}
                <img
                  loading="lazy"
                  src={require("../images/logo.jpg")} // Ensure this path is correct
                  className="w-[120px] mb-6"
                  alt="Madad Fintech Logo"
                />

                {/* Title */}
                <div className="text-xl font-medium text-[#333333] mb-8 text-center">
                  Login to Access Madad
                </div>

                {/* Tab Buttons Container */}
                <div className="self-stretch flex w-full">
                  {/* Via Mobile Otp Tab Button */}
                  <button
                    className={`flex-1 px-4 py-3 text-sm text-center transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 rounded-t-lg border-r border-gray-300 ${ // Added border-gray-300 for consistent separator if needed. Adjust color if Screenshot.png shows different.
                      loginMethod === "mobile"
                        ? "bg-[#f1f6f7] text-gray-800 font-semibold" // Active: grey bg, dark bold text
                        : "bg-white text-gray-500 font-medium hover:bg-gray-50" // Inactive: white bg, lighter text
                      }`}
                    onClick={() => loginMethod !== "mobile" && toggleLoginMethod()}
                  >
                    Via Mobile Otp
                  </button>

                  {/* Email Address Tab Button */}
                  <button
                    className={`flex-1 px-4 py-3 text-sm text-center transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 rounded-t-lg ${loginMethod === "email"
                      ? "bg-[#f1f6f7] text-gray-800 font-semibold" // Active: grey bg, dark bold text
                      : "bg-white text-gray-500 font-medium hover:bg-gray-50" // Inactive: white bg, lighter text
                      }`}
                    onClick={() => loginMethod !== "email" && toggleLoginMethod()}
                  >
                    Email Address
                  </button>
                </div>
                {loginMethod === "mobile" && !isOtpSent ? (
                  <>
                    <div className="w-full space-y-4 bg-[#f1f6f7] rounded-b-lg p-6 shadow-sm">
                      {/* Mobile Input Section */}
                      <div className="flex flex-col space-y-3">
                        {/* MODIFIED LABEL COLOR */}
                        <label className="text-sm text-green-600"> {/* Changed to text-green-600 from text-gray-700 */}
                          Enter your mobile number to access this app
                        </label>
                        <div className="flex items-center border border-gray-300 rounded-md overflow-hidden bg-white">
                          <div className="relative">
                            <div
                              className="flex items-center px-3 py-3 bg-gray-50 border-r border-gray-300 cursor-pointer min-w-[80px]"
                              onClick={(e) => { e.stopPropagation(); setIsDropdownOpen(!isDropdownOpen); }}
                            >
                              <span className="text-lg mr-1">🇶🇦</span>
                              <span className="text-sm font-medium text-gray-700">+974</span>
                              <svg
                                className={`w-3 h-3 text-gray-500 ml-1 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
                                fill="none" stroke="currentColor" viewBox="0 0 24 24"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                              </svg>
                            </div>
                            {isDropdownOpen && (
                              <div className="absolute top-full left-0 mt-1 w-64 max-h-60 overflow-y-auto bg-white border border-gray-300 rounded-md shadow-lg z-10">
                                {countries.filter(c => allowedCountries.includes(c.code)).map(c => (
                                  <div key={c.code} className="flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleCountrySelect(c)}>
                                    <div className="mr-2">{getCountryFlagEmojiFromCountryCode(c.code)}</div>
                                    <div className="flex-1">{c.name}</div>
                                    <div className="text-gray-500">{c.dialCode}</div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                          <input
                            type="text" value={phone} onChange={handlePhoneChange}
                            className="flex-1 py-3 px-3 text-base text-gray-800 bg-white focus:outline-none"
                            placeholder="Mobile Number" autoFocus
                          />
                        </div>
                        {phoneError && <div className="text-red-500 text-sm">{phoneError}</div>}
                      </div>
                      {loginError && <div className="text-red-500 text-sm text-center">{loginError}</div>}

                      {/* Send OTP Button - MODIFIED DISABLED STATE */}
                      <button
                        className={`w-full py-3 text-base font-medium rounded-md transition-all duration-200 ${phone.length > 7 && !isSendingOtp
                          ? "text-white bg-[#004141] hover:bg-[#003030]"  // YOUR ORIGINAL ENABLED STYLE - kept as is
                          : "bg-gray-300 text-gray-500 cursor-not-allowed" // MODIFIED DISABLED: darker bg for contrast on bg-[#f1f6f7]
                          }`}
                        onClick={phone.length > 7 && !isSendingOtp ? handleSendOtpClick : undefined}
                        disabled={!(phone.length > 7 && !isSendingOtp)}
                      >
                        {isSendingOtp ? "Sending..." : "Send Otp"}
                      </button>

                      <div className="relative py-4">
                        <div className="absolute inset-0 flex items-center"><div className="w-full border-t border-gray-300"></div></div>
                        <div className="relative flex justify-center"><span className="px-4 text-sm text-gray-500 bg-[#f1f6f7]">or continue with</span></div>
                      </div>

                      {GoogleLogin}
                      <div className="text-center text-sm">
                        <span className="text-gray-600">Don't have an account? </span>
                        <span className="text-blue-600 font-medium hover:underline cursor-pointer" onClick={() => setIsSignUp(!isSignUp)}>Sign Up</span>
                      </div>
                      <div className="text-center text-xs text-gray-500 mt-4">
                        <p>By clicking continue, you agree to our <span className="text-blue-600 hover:underline cursor-pointer" onClick={handleTermsClick}>Terms of Service</span> and <span className="text-blue-600 hover:underline cursor-pointer" onClick={handlePrivacyClick}>Privacy Policy</span></p>
                      </div>
                    </div>
                  </>
                ) : loginMethod === "email" && isSignUp ? (
                  <>
                    <div className="w-full space-y-6 bg-[#f1f6f7] rounded-b-lg p-6 shadow-sm">
                      {/* Ensure labels and "OR" span backgrounds are correct for bg-[#f1f6f7] if this view is used */}
                      <div className="flex flex-col space-y-2">
                        <label className="text-sm text-green-600">Business Email Address</label> {/* Assuming green label consistency */}
                        <input type="email" value={email} onChange={handleEmailChange} className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none" placeholder="<EMAIL>" autoFocus />
                        {emailError && <div className="text-red-500 text-sm">{emailError}</div>}
                      </div>
                      <div className="flex flex-col space-y-2">
                        <label className="text-sm text-gray-700">Password</label> {/* Or green if preferred */}
                        <input type="password" value={password} onChange={handlePasswordChange} className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none" placeholder="Enter your password" />
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="font-medium">Password must contain:</p>
                          <ul className="list-disc list-inside space-y-1">
                            <li className={passwordValidation.length ? "text-green-600" : "text-gray-500"}>At least 12 characters</li>
                            <li className={passwordValidation.uppercase ? "text-green-600" : "text-gray-500"}>At least one uppercase letter</li>
                            <li className={passwordValidation.lowercase ? "text-green-600" : "text-gray-500"}>At least one lowercase letter</li>
                            <li className={passwordValidation.number ? "text-green-600" : "text-gray-500"}>At least one number</li>
                            <li className={passwordValidation.specialChar ? "text-green-600" : "text-gray-500"}>At least one special character</li>
                          </ul>
                        </div>
                        {passwordError && <div className="text-red-500 text-sm">{passwordError}</div>}
                      </div>
                      <div className="flex flex-col space-y-2">
                        <label className="text-sm text-gray-700">Confirm Password</label> {/* Or green */}
                        <input type="password" value={confirmPassword} onChange={handleConfirmPasswordChange} className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none" placeholder="Confirm your password" />
                        {confirmPasswordError && <div className="text-red-500 text-sm">{confirmPasswordError}</div>}
                      </div>
                      <button
                        className={`w-full py-2.5 text-lg font-medium rounded-md transition-all duration-200 ${email && password && confirmPassword && password === confirmPassword && !isLoggingIn ? "text-white bg-[#004141] shadow-lg" : "text-gray-400 bg-gray-100 cursor-not-allowed"}`}
                        onClick={email && password && confirmPassword && password === confirmPassword && !isLoggingIn ? handleEmailSignUp : undefined}
                        disabled={!(email && password && confirmPassword && password === confirmPassword && !isLoggingIn)}
                      >
                        Sign Up
                      </button>
                      <div className="relative py-3">
                        <div className="absolute inset-0 flex items-center"><div className="w-full border-t border-gray-300"></div></div>
                        <div className="relative flex justify-center"><span className="px-4 text-sm text-gray-500 bg-[#f1f6f7]">OR</span></div>
                      </div>
                      {GoogleLogin}
                      <div className="text-center text-sm">
                        <span className="text-gray-600">Already have an account?</span>
                        <span className="ml-1 text-[#208039] font-medium hover:underline cursor-pointer" onClick={() => setIsSignUp(false)}>Log in</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="w-full space-y-6 bg-[#f1f6f7] rounded-b-lg p-6 shadow-sm">
                      {/* Ensure labels and "OR" span backgrounds are correct for bg-[#f1f6f7] if this view is used */}
                      <div className="flex flex-col space-y-2">
                        <label className="text-sm text-green-600">Business Email Address</label> {/* Assuming green label consistency */}
                        <input type="email" value={email} onChange={handleEmailChange} className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none" placeholder="<EMAIL>" autoFocus />
                        {emailError && <div className="text-red-500 text-sm">{emailError}</div>}
                      </div>
                      <div className="flex flex-col space-y-2 mt-4 mb-4">
                        <label className="text-sm text-gray-700">Password</label> {/* Or green */}
                        <input type="password" value={password} onChange={handlePasswordChange} className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none" placeholder="Enter your password" />
                        {passwordError && <div className="text-red-500 text-sm">{passwordError}</div>}
                      </div>
                      <div className="flex justify-end">
                        <button className="text-sm text-[#004141] font-medium hover:underline">Forgot password?</button>
                      </div>
                      <button
                        className={`w-full text-lg py-2 font-medium rounded-md transition-all duration-200 ${email && password && !isLoggingIn ? "text-white bg-[#004141] shadow-lg" : "text-gray-400 bg-gray-200 cursor-not-allowed"}`}
                        onClick={email && password && !isLoggingIn ? handleEmailLogin : undefined}
                        disabled={!(email && password && !isLoggingIn)}
                      >
                        {isLoggingIn ? "Logging In..." : "Log In"}
                      </button>
                      {loginError && <div className="text-red-500 text-sm text-center">{loginError}</div>}
                      <div className="relative">
                        <div className="absolute inset-0 flex items-center"><div className="w-full border-t border-gray-300"></div></div>
                        <div className="relative flex justify-center"><span className="px-4 text-sm text-gray-500 bg-[#f1f6f7]">OR</span></div>
                      </div>
                      {GoogleLogin}
                      <div className="text-center text-sm">
                        <span className="text-gray-600">Don't have an account?</span>
                        <span className="ml-1 text-[#208039] font-medium hover:underline cursor-pointer" onClick={() => setIsSignUp(true)}>Sign up</span>
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Footer */}
              <div className="w-full mt-8 text-sm text-center text-gray-500 space-y-2">
                <p>Your data is 100% safe with us</p>
                <p>© Madad Fintech. All rights reserved</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel (Image and Text) */}
      <div className="hidden md:flex w-full md:w-1/2 bg-cover bg-center relative flex-col justify-end items-start p-8 sm:p-12 text-white order-1 md:order-2 min-h-[300px] md:min-h-screen transition-all duration-700 ease-in-out overflow-hidden"
        style={{ backgroundImage: `url(${backdrop})` }}>

        <div className="absolute inset-0 bg-black opacity-40"></div>

        {/* Animated Text Block with Slide Animation */}
        <div className="relative z-10 mb-10 w-full">
          <div
            key={index} // this triggers transition on change
            className="transform transition-all duration-500 ease-in-out"
            style={{
              transform: 'translateX(0)',
              opacity: 1,
              animation: 'slideIn 0.5s ease-in-out forwards'
            }}
          >
            <h2
              className="text-3xl lg:text-4xl font-bold mb-3 leading-tight"
              style={{
                transform: 'translateX(0)',
                opacity: 1,
                transition: 'all 0.5s ease-in-out'
              }}
            >
              {messages[index].title}
            </h2>
            <p
              className="text-base lg:text-lg"
              style={{
                transform: 'translateX(0)',
                opacity: 1,
                transition: 'all 0.5s ease-in-out 0.1s'
              }}
            >
              {messages[index].text}
            </p>
          </div>
        </div>

        {/* Animated Dots */}
        <div className="absolute bottom-10 left-8 sm:left-12 z-10 flex space-x-2">
          {messages.map((_, i) => (
            <span
              key={i}
              className={`block w-2 h-2 sm:w-2.5 sm:h-2.5 bg-white rounded-full transition-all duration-300 transform ${i === index
                ? 'opacity-100 scale-110'
                : 'opacity-50 scale-100 hover:opacity-75'
                }`}
              style={{
                transform: i === index ? 'scale(1.1)' : 'scale(1)',
                opacity: i === index ? 1 : 0.5
              }}
            />
          ))}
        </div>

        {/* Inline CSS Animation Keyframes */}
        <style dangerouslySetInnerHTML={{
          __html: `
      @keyframes slideIn {
        0% {
          transform: translateX(30px);
          opacity: 0;
        }
        100% {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes slideOut {
        0% {
          transform: translateX(0);
          opacity: 1;
        }
        100% {
          transform: translateX(-30px);
          opacity: 0;
        }
      }
    `
        }} />
      </div>
    </div>
  );

};

export default LoginPage;