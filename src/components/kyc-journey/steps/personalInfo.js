import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import SharedCache from '../../../sharedCache';
// Assuming these API functions exist in this path
import { getKycInfo, initiateVideoKyc, getVideoKycVerificationStatus, updateKyc } from '../../../api/kyc';
import config from '../../../config.json';
import {
    ClockIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
    ArrowRightCircleIcon,
    QuestionMarkCircleIcon,
    VideoCameraIcon, // Icon for the Video KYC button
    XMarkIcon // Icon for modal close button
} from '@heroicons/react/24/outline';
import LoadingModal from '../../Reusable/Loading';


// Helper function (keep as is)
const getNested = (obj, path, defaultValue = null) => {
    try {
        const value = path.split('.').reduce((o, k) => (o || {})[k], obj);
        return value === undefined || value === null ? defaultValue : value;
    } catch (e) {
        return defaultValue;
    }
};

const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait a moment while we load your journey data.' />;
};


const ShareholderKycStatus = ({ onBack, onNext }) => {
    const [userData, setUserData] = useState(null);
    const [isLoading, setIsLoading] = useState(true); // Loading initial page data
    const [error, setError] = useState('');
    const history = useHistory();
    const [kycStatus, setKycStatus] = useState(null); // Overall KYC status
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isVideoKycAttempted, setIsVideoKycAttempted] = useState(false); // Tracks if modal *was* opened
    const [isInitiatingKyc, setIsInitiatingKyc] = useState(false); // Loading state for the init API call
    const [iframeSrc, setIframeSrc] = useState(''); // State to hold the dynamic iframe URL from API
    const [isCheckingStatus, setIsCheckingStatus] = useState(false); // Loading state for status check after close
    const [isSubmitting, setIsSubmitting] = useState(false); // ADD THIS
    const [submitError, setSubmitError] = useState('');     // ADD THI
    // Removed static kycJourneyLink

    // sendBulkEmails - keep as is, but maybe update the link later if needed
    const sendBulkEmails = async (emails) => {
        try {
            const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-bulk-emails`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', },
                body: JSON.stringify({
                    emails: emails || [],
                    subject: 'KYC Verification',
                    // Consider if this email link should also be dynamic or removed if KYC is only via app
                    html: `Your KYC has been initiated, <a href="https://app.shuftipro.com/verification/journey/SxkSHnDo1745353789" target="_blank">click here to start the KYC process</a>.`
                }),
            });
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            console.log("Bulk emails sent:", data);
        } catch (error) {
            console.error("Error sending bulk emails:", error);
        }
    };

    // fetchData - Initial data load (keep as is)
    const fetchData = useCallback(async (userId) => {
        setIsLoading(true);
        setError('');
        try {
            console.log(`Workspaceing KYC info for status page: userId=${userId}`);
            const response = await getKycInfo(userId); // Assumes this fetches the full user object
            console.log("getKycInfo Response:", response);
            if (response?.success && response?.user) {
                const data = response.user;
                const mainKycStatus = getNested(data, 'kyc.verificationStatus', null);
                setKycStatus(mainKycStatus);
                data.shareholders = data.shareholders || [];
                setUserData(data); // Store the full user data
                console.log("User data set for status page:", data);

                // Update isVideoKycAttempted based on fetched status if needed
                const currentVideoStatus = getNested(data, 'kyc.videoKyc.status', 'NOT_ATTEMPTED');
                if (currentVideoStatus !== 'NOT_ATTEMPTED') {
                    setIsVideoKycAttempted(true);
                }

                // Email logic (keep or adjust as needed)
                // if (mainKycStatus === 'INITIATED' || !mainKycStatus) {
                //    await sendBulkEmails(data?.shareholders?.map(sh => sh.email).filter(Boolean));
                // }
            } else {
                setError(response?.message || "Could not retrieve user data.");
                setKycStatus(null);
            }
        } catch (err) {
            console.error("Error fetching data for status page:", err);
            setError(`Error loading data: ${err.message}.`);
            setKycStatus(null);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // useEffect for initial data load and email sending
    // useEffect for initial data load and sending emails once on mount
    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";

        if (!userId) {
            setError("User ID not found. Please log in again.");
            setIsLoading(false);
            return;
        }

        const fetchDataAndSendEmails = async () => {
            // Fetch user data first
            await fetchData(userId);

            // Access the *latest* userData state directly here.
            // Although userData is not a dependency, this code runs
            // within the same async flow triggered by the effect,
            // shortly after fetchData has likely completed and updated state.
            // For this pattern (initial fetch then action), this is generally safe.

            // Access the current state value - this can be slightly tricky
            // if state updates are very rapid, but for initial load is standard.
            // A more robust way for complex scenarios might involve a ref
            // to track if emails were sent. For this case, relying on
            // fetchData completing before the next lines execute is typical.

            // A slight delay or checking the state value after a tick might be
            // needed in more complex scenarios, but let's try direct access first.
            // console.log("Checking userData after fetchData:", userData); // Debugging line

            // Using a simple check based on the assumption that setUserData
            // from fetchData will complete before the email sending code runs
            // within the same async function.
            // Alternatively, you could pass the fetched user data directly
            // from fetchData if it returned the data instead of just updating state.

            // Re-fetch user data directly inside this async function to ensure latest state
            // try {
            //     const response = await getKycInfo(userId); // Fetch again just for this logic
            //     if (response?.success && response?.user) {
            //         const latestUserData = response.user;
            //         const shareholderEmails = latestUserData.shareholders?.map(sh => sh.email).filter(Boolean) || [];
            //         const allEmails = [...shareholderEmails, latestUserData?.email].filter(Boolean); // Include user's primary email

            //         // Add a condition to send emails only if it hasn't been sent before
            //         // You would need a state or ref to track this. Let's add a simple state flag.
            //         // For this example, let's assume sending once per page load is the goal.
            //         // If you need to prevent sending on subsequent renders due to other state changes,
            //         // you'll need a ref.

            //         // Let's use a simple flag to prevent sending on subsequent renders IF
            //         // the effect re-ran due to other dependencies (fetchData or sendBulkEmails, though stable).
            //         // For a true "only once on mount" behavior, an empty dependency array is best,
            //         // but that wouldn't allow using fetchData or sendBulkEmails easily without refs
            //         // or restructuring.

            //         // A better approach for "send once after initial fetch":
            //         // Use a ref to track if emails have been sent.

            //         // We need to refactor slightly to use a ref reliably.
            //         // Let's stick to the simpler approach first and address
            //         // the ref if needed, but the primary issue is userData dependency.

            //         // Let's remove userData from deps and trust the async flow
            //         // along with potentially re-fetching the data here for the email logic.

            //         if (allEmails.length > 0) {
            //             console.log("Attempting to send bulk emails to:", allEmails);
            //             await sendBulkEmails(allEmails);
            //         } else {
            //             console.log("No valid email addresses found for bulk sending.");
            //         }
            //     }
            // } catch (err) {
            //     console.error("Error re-fetching data for email sending:", err);
            //     // Handle error if needed
            // }
        };

        fetchDataAndSendEmails();

    }, []); // Removed userData from dependencies

    // useEffect for initial data load (keep as is)
    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";
        if (!userId) {
            setError("User ID not found. Please log in again.");
            setIsLoading(false);
            return;
        }
        fetchData(userId);
    }, [fetchData]);


    // UPDATED Function to handle opening the modal
    const handleOpenModal = async () => {
        setIsInitiatingKyc(true);
        setError('');
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";
        if (!userId) {
            setError("User ID not found. Cannot initiate KYC.");
            setIsInitiatingKyc(false);
            return;
        }
        try {
            const result = await initiateVideoKyc(userId, null); // Call backend to initiate
            if (result.success && result.verificationUrl) {
                console.log("Video KYC initiated successfully. Ref:", result.reference);
                setIframeSrc(result.verificationUrl); // Set dynamic URL
                setIsModalOpen(true); // Open modal
            } else {
                console.error("Failed to initiate Video KYC:", result.error);
                const errorMessage = `Failed to start Video KYC: ${result.error || 'Unknown error from server.'}`;
                setError(errorMessage);
                alert(errorMessage);
                setIframeSrc('');
                setIsModalOpen(false);
            }
        } catch (apiError) {
            console.error("Error calling initiation API:", apiError);
            const errorMessage = `Error initiating Video KYC: ${apiError.message || 'Please check network connection.'}`;
            setError(errorMessage);
            alert(errorMessage);
            setIframeSrc('');
            setIsModalOpen(false);
        } finally {
            setIsInitiatingKyc(false);
        }
    };

    // UPDATED Function to handle closing the modal
    const handleCloseModal = async () => {
        setIsModalOpen(false);
        setIsVideoKycAttempted(true);
        setIframeSrc('');
        setIsCheckingStatus(true); // Start status check loading
        setError('');
        console.log("Video KYC modal closed. Fetching latest status immediately...");
        try {
            const user = SharedCache.get('user') || {};
            const userId = user._id || user.id || "";
            const statusResult = await getVideoKycVerificationStatus(userId); // Call status check API
            if (statusResult.success) {
                console.log("Fetched latest status:", statusResult);
                // Update component state with the fetched status by merging into userData
                setUserData(prevData => {
                    const prevKyc = prevData?.kyc || {};
                    const prevVideoKyc = prevKyc.videoKyc || {};
                    return {
                        ...prevData,
                        kyc: {
                            ...prevKyc,
                            verificationStatus: statusResult.overallKycStatus,
                            videoKyc: {
                                ...prevVideoKyc,
                                status: statusResult.videoKycStatus,
                                declineReason: statusResult.declineReason
                            }
                        }
                    };
                });
                setKycStatus(statusResult.overallKycStatus); // Update overall status state
            } else {
                console.error("Failed to fetch latest status:", statusResult.error);
                alert(`Could not retrieve the latest KYC status automatically. The status shown might be outdated. Error: ${statusResult.error}`);
            }
        } catch (fetchError) {
            console.error("Error calling status fetch API:", fetchError);
            alert(`An error occurred while fetching the latest KYC status. The status shown might be outdated.`);
        } finally {
            setIsCheckingStatus(false); // Stop status check loading
        }
    };

    // renderStatus - Keep as is (renders status badges)
    const renderStatus = (status) => {
        let colorClass = 'text-gray-800 bg-gray-100';
        let icon = <QuestionMarkCircleIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
        let statusText = status || 'UNKNOWN';
        switch (status) {
            case 'APPROVED': case 'VERIFIED':
                colorClass = 'text-green-800 bg-green-100'; icon = <CheckCircleIcon className="w-4 h-4 mr-1.5 text-green-500" aria-hidden="true" />; statusText = 'Approved'; break;
            case 'INITIATED':
                colorClass = 'text-gray-800 bg-gray-100'; icon = <ClockIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />; statusText = 'Initiated'; break;
            case 'REINITIATED':
                colorClass = 'text-blue-800 bg-blue-100'; icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-blue-500" aria-hidden="true" />; statusText = 'Re-initiated'; break;
            case 'UNDER_REVIEW': case 'REVIEW': case 'PENDING':
                colorClass = 'text-yellow-800 bg-yellow-100'; icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />; statusText = 'Under Review'; break;
            case 'INFO_NEEDED':
                colorClass = 'text-orange-800 bg-orange-100'; icon = <ExclamationTriangleIcon className="w-4 h-4 mr-1.5 text-orange-500" aria-hidden="true" />; statusText = 'Info Needed'; break;
            case 'REJECTED':
                colorClass = 'text-red-800 bg-red-100'; icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />; statusText = 'Rejected'; break;
            default: statusText = status ? status.replace(/_/g, ' ') : 'Unknown'; break;
        }
        return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} whitespace-nowrap`}>{icon}{statusText}</span>;
    };

    // renderCompletionDate - Keep as is
    const renderCompletionDate = (dateString) => {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';
            return date.toLocaleDateString();
        } catch (e) {
            console.error("Error parsing date:", dateString, e); return '-';
        }
    };

    // handleResend - Keep as is (uses sendBulkEmails)
    const handleResend = async (email, name) => {
        if (!email || email === 'N/A') { alert('Cannot resend: Email address is missing.'); return; }
        alert(`Attempting to resend eKYC link to ${name || 'the user'} at ${email}.`);
        try { await sendBulkEmails([email]); alert(`eKYC link resent successfully to ${email}.`); }
        catch (error) { alert(`Failed to resend eKYC link to ${email}. Error: ${error.message}`); }
    };

    const handleSubmit = async () => {
        setSubmitError('');
        setIsSubmitting(true);

        const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
        if (!userId) {
            setSubmitError('User ID not found. Please log in again.');
            setIsSubmitting(false);
            return;
        }

        const finalPayload = {
            id: userId,
            kyc: {
                verificationStatus: "UNDER_REVIEW",
                applicationStatus: {
                    registration: { status: "Submitted" },
                    creditEvaluation: { status: "Submitted" },
                    creditLine: { status: "Submitted" }
                }
            }
        };

        console.log("Calling final updateKyc with payload:", JSON.stringify(finalPayload, null, 2));

        try {
            const updateResponse = await updateKyc(finalPayload);
            if (!updateResponse.success) {
                throw new Error(updateResponse.message || "Failed to submit the application.");
            }

            console.log("Successfully submitted application.");
            history.push('/dashboard'); // Redirect to dashboard on success

        } catch (error) {
            console.error('Error during final submission:', error);
            setSubmitError(`An error occurred: ${error.message || 'Unknown error'}. Please try again.`);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Loading/Error States - Keep as is
    if (isLoading) { /* ... Loading JSX ... */
        return <LoadingPopup />;
    }
    // Note: Keep the top-level error display, but clear specific API errors with setError('') before new calls
    // if (error && !isInitiatingKyc && !isCheckingStatus) { /* ... Error JSX ... */
    //      return ( <div className="max-w-4xl mx-auto p-6 my-10 border border-red-400 bg-red-50 text-red-700 rounded-md text-center"><ExclamationTriangleIcon className="h-6 w-6 inline mr-2" />{error}</div> );
    // }

    // Prepare shareholder data (adjust nested paths if needed)
    const shareholdersToDisplay = (userData?.shareholders || []).map((sh, index) => ({
        id: sh._id || `sh-${index}`,
        name: `${sh.firstName || ''} ${sh.lastName || ''}`.trim() || `Shareholder ${index + 1}`,
        // Assuming shareholder status is directly on the shareholder object
        kycVerificationStatus: sh.kycVerificationStatus || null,
        email: sh.email || 'N/A',
        type: 'Shareholder',
        // Use shareholder's modifiedOn or a specific completion date if available
        completionDate: sh.modifiedOn, // Or getNested(sh, 'kyc.completionDate', sh.modifiedOn)
    }));

    // Determine if the "Next" button should be enabled
    // Now potentially based on actual *approved* status, not just attempted
    // Example: Enable Next only if overall KYC is Approved
    const overallStatusFromState = getNested(userData, 'kyc.verificationStatus');
    // Simple logic: attempt needed. More robust: check overallStatusFromState === 'APPROVED'
    const isNextButtonEnabled = isVideoKycAttempted; // Or: overallStatusFromState === 'APPROVED';


    // --- Render JSX ---
    return (
        <>
            {/* MODAL for Video KYC - remains at the top level */}
            {isModalOpen && (
                <div className="fixed inset-0 z-50 overflow-y-auto">
                    <div className="flex items-center justify-center min-h-screen p-4">
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
                        <div className="rounded-lg transform w-full max-w-4xl">
                            <div className="flex justify-between items-center p-4 border-b">
                                <h3 className="text-lg font-medium text-gray-900">Video KYC Verification</h3>
                                <button type="button" className="text-gray-400 hover:text-gray-500" onClick={handleCloseModal}>
                                    <XMarkIcon className="h-6 w-6" />
                                </button>
                            </div>
                            <div className="p-1 h-[75vh]">
                                {iframeSrc ? (
                                    <iframe
                                        src={iframeSrc}
                                        title="Video KYC Verification"
                                        className="w-full h-full border-0"
                                        allow="camera; microphone"
                                    ></iframe>
                                ) : (
                                    <div className="flex items-center justify-center h-full"><ArrowPathIcon className="animate-spin h-8 w-8 text-gray-500" /></div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="flex w-full min-h-screen">
                {/* === LEFT SIDE (Content & Centering Wrapper) === */}
                <div className="w-full lg:w-8/12 flex flex-col items-center justify-center p-4 md:p-8 bg-gray-50 relative">
                    {/* Step Counter - moved to top right */}
                    <div className="absolute top-4 right-4 text-sm font-semibold text-gray-500">
                        Step 3 of 3
                    </div>

                    <div className="w-full max-w-5xl">
                        {/* Original content card */}
                        <div className='rounded-lg overflow-hidden'>
                            <div className="px-6 py-4">
                                <h1 className="text-2xl font-bold text-gray-800 text-center">
                                    Shareholder(s) eKYC Status
                                </h1>
                                <p className="mt-1 text-sm text-gray-600 text-center">
                                    Please complete the eKYC to process your application.
                                </p>
                            </div>

                            <div className="p-4 bg-gray-200 rounded-lg   sm:p-6">
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-300">
                                        <thead className="bg-gray-100">
                                            <tr>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">Name</th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">Email</th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">KYC Status</th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {shareholdersToDisplay.map((person) => (
                                                <tr key={person.id}>
                                                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.name}</td>
                                                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{person.email}</td>
                                                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{renderStatus(person.kycVerificationStatus)}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            {/* Note and Action Buttons */}
                            <div className="px-4 sm:px-6 py-6 border-t border-gray-200">
                                {submitError && (
                                    <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded-md text-sm text-center">
                                        {submitError}
                                    </div>
                                )}
                                <div className="mb-6 p-4 bg-yellow-50 border border-yellow-300 rounded-md text-center">
                                    <p className="text-sm text-yellow-800">
                                        <strong>Note:</strong> By submitting, you agree that we will share email to your shareholders and buyers to verify.
                                    </p>
                                </div>
                                <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                                    <button
                                        type="button"
                                        onClick={handleSubmit} // Changed from handleFinish
                                        disabled={isSubmitting}
                                        className="w-full sm:w-auto inline-flex items-center justify-center px-8 py-3 font-medium rounded-md text-white bg-[#003a39] hover:bg-[#005554] disabled:bg-gray-400 transition-colors"
                                    >
                                        {isSubmitting ? (
                                            <ArrowPathIcon className="animate-spin h-5 w-5 mr-2" />
                                        ) : (
                                            <ArrowRightCircleIcon className="h-5 w-5 mr-2" />
                                        )}
                                        {isSubmitting ? 'Submitting...' : 'Submit and Finish'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* === RIGHT SIDE (INFORMATIONAL PANEL) === */}
                <div className="hidden lg:flex w-4/12 bg-gradient-to-br from-[#E6F9F4] to-white p-10 flex-col items-center justify-center border-l">
                    <div className="max-w-md w-full">
                        <img src={require("../../../images/salor_telescope.png")} alt="Credit Line Application" className="w-48 h-auto mx-auto mb-6" />
                        <h2 className="text-2xl font-bold text-center text-[#003a39] mb-4">
                            Credit Line Application
                        </h2>
                        <p className="text-center text-gray-600 text-sm mb-8">
                            Unlock a pre-approved credit amount you can use anytime—no need to reapply for every invoice. Enjoy quick access to funds, improved cash flow, and the freedom to grow your business on your terms.
                        </p>
                        <div className="space-y-5 text-gray-700 text-left">
                            {/* Item 1: FILLED */}
                            <div className="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1">
                                    <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                                </svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Shareholder details</h3>
                                    <p className="text-sm text-gray-600">
                                        Providing shareholder information helps us ensure transparency, assess business ownership, and comply with regulatory and lender requirements. It strengthens your credibility during verification and helps build trust with financial partners for faster funding approvals.
                                    </p>
                                </div>
                            </div>
                            {/* Item 2: FILLED */}
                            <div className="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1">
                                    <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                                </svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Buyer details</h3>
                                    <p className="text-sm text-gray-600">
                                        Sharing your buyer details helps us assess the credibility of your invoices and connect you with the right lending partners. It ensures faster verification, accurate risk evaluation, and better funding offers tailored to your business transactions.
                                    </p>
                                </div>
                            </div>
                            {/* Item 3: OUTLINE */}
                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">eKYC</h3>
                                    <p className="text-sm text-gray-600">
                                        eKYC helps us verify your identity and business credentials quickly and securely. It's a mandatory step to comply with regulatory guidelines and ensures a smooth, trusted process for accessing funding through our platform.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ShareholderKycStatus;