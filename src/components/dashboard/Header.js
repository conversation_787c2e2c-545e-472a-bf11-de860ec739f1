import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { fetchUserById, logout } from '../../api/auth';
import {
    BellIcon,
    UserCircleIcon,
    Bars3Icon, // For Hamburger Menu
    XMarkIcon, // For Closing Mobile Menu
    ChartBarIcon, // Used for Overview
    ClipboardDocumentListIcon, // Used for My Invoices/Invoices for Approval
    BanknotesIcon,
    ArrowDownCircleIcon,
    Cog6ToothIcon, // Used for Profile
    ChatBubbleOvalLeftEllipsisIcon, // Used for Help and Support
    CheckCircleIcon,
    UserGroupIcon,
    ArrowDownIcon,
    ArrowLeftStartOnRectangleIcon, // For My Buyers
} from '@heroicons/react/24/outline';
import config from '../../config';
import SharedCache from '../../sharedCache';
import { LayoutContext } from '../../MainLayout'; // Correct import for LayoutContext
import { useUser } from '../../contexts/UserContext';
import { decryptData } from '../../utils/Masking';

const Header = () => { // Removed currentPage, setCurrentPage props as it gets them from LayoutContext/useUser
    const { currentPage, setCurrentPage } = useContext(LayoutContext); // Get from LayoutContext
    const { userType, isBuyer, isMsme, toggleUserType, isDualRoleUser } = useUser();
    console.log("DUAL ROLE", isDualRoleUser);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
    const [userName, setUserName] = useState('');
    const [notifications, setNotifications] = useState([]);
    const [userKycStatus, setUserKycStatus] = useState('');
    const [creditLineStatus, setCreditLineStatus] = useState('');
    const [hasActiveCreditLine, setHasActiveCreditLine] = useState(false);
    const history = useHistory();
    const location = useLocation();
    const [mobileNavOpen, setMobileNavOpen] = useState(false);
    const mobileNavRef = useRef(null);
    const hamburgerIconRef = useRef(null);
    const profileDropdownRef = useRef(null);
    const profileIconRef = useRef(null);
    const notificationDropdownRef = useRef(null);
    const bellIconRef = useRef(null);
    const mobileTrayContentRef = useRef(null);

    const user = SharedCache.get("user") || {};
    const userId = user._id;

    const getPageDisplayName = () => {
        const currentPath = location.pathname;

        // Common routes first
        if (currentPath.startsWith('/kyc')) { return 'Onboarding'; }
        if (currentPath.startsWith('/invoiceContract')) { return 'Invoice Contract'; }
        if (currentPath.startsWith('/creditLineContract')) { return 'Credit Line Contract'; }
        if (currentPath === '/eligibility-checker') { return 'Eligibility Check'; }
        if (currentPath === '/support') { return 'Help and Support'; }
        if (currentPath === '/documents') { return 'Documents'; }
        if (currentPath === '/uploadInvoice') { return 'Upload Invoice'; }
        if (currentPath === '/terms-and-conditions') { return 'Terms and Conditions'; }
        if (currentPath === '/privacy-policy') { return 'Privacy Policy'; }
        if (currentPath === '/verify-email') { return 'Verify Email'; }
        if (currentPath === '/digilockerResponse') { return 'Digilocker Response'; }
        if (currentPath === '/digilockerResponseInvoiceFinancing') { return 'Digilocker Response - Invoice Financing'; }

        // User type specific displays
        if (isBuyer) {
            switch (currentPage) { // Use currentPage from LayoutContext for active section
                case 'buyer-dashboard': return 'Buyer Dashboard';
                case 'invoices-approval': return 'Invoices for Approval'; // New page title
                default: return 'Buyer Dashboard';
            }
        } else if (isMsme) {
            switch (currentPage) { // Use currentPage from LayoutContext for active section
                case 'dashboard': return 'Dashboard Overview';
                case 'my-buyers': return 'My Buyers';
                case 'my-invoices': return 'My Invoices';
                case 'payments': return 'Payments and Disburses';
                default: return 'Dashboard Overview';
            }
        }
        return 'Dashboard'; // Fallback
    };

    useEffect(() => {
        const fetchUserSpecificData = async (id) => {
            // For MSME users, fetch their specific statuses
            if (isMsme) {
                try {
                    // Call our centralized function
                    const response = await fetchUserById(id);

                    // Check if the response was successful (status 200)
                    if (response && response.status === 200) {
                        const data = response.data; // Get the user object from the .data property

                        const fullName = [data.firstName, data.middleName, data.lastName].filter(Boolean).join(' ');
                        setUserName(fullName);

                        const currentVerificationStatus = data.kyc?.verificationStatus || '';
                        setUserKycStatus(currentVerificationStatus);

                        // Conditionally fetch credit line data
                        if (currentVerificationStatus !== 'INITIATED' && currentVerificationStatus !== 'REINITIATED') {
                            const creditLineResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${id}`);
                            const creditLineData = await creditLineResponse.json();
                            if (creditLineResponse.ok && creditLineData?.creditLineStatus) {
                                setCreditLineStatus(creditLineData.creditLineStatus);
                                setHasActiveCreditLine(creditLineData.creditLineStatus === 'ACTIVE');
                            } else {
                                console.error("Failed to fetch credit line status:", creditLineData);
                                setCreditLineStatus('INACTIVE');
                                setHasActiveCreditLine(false);
                            }
                        } else {
                            setCreditLineStatus(null);
                            setHasActiveCreditLine(false);
                        }
                    } else {
                        // This block handles API errors or decryption failures from fetchUserById
                        console.error("Failed to fetch MSME user data:", response?.data?.message || 'Unknown error');
                        setUserName('');
                        setUserKycStatus('INITIATED');
                        setCreditLineStatus(null);
                        setHasActiveCreditLine(false);
                    }
                } catch (err) {
                    console.error("Critical error fetching MSME info or credit line:", err);
                    setUserName('');
                    setUserKycStatus('INITIATED');
                    setCreditLineStatus(null);
                    setHasActiveCreditLine(false);
                }
            } else if (isBuyer) {
                const buyerData = SharedCache.get("user");
                if (buyerData) {
                    setUserName(buyerData.lenderName || buyerData.username || '');
                }
                setUserKycStatus('');
                setCreditLineStatus(null);
                setHasActiveCreditLine(false);
            } else {
                // Clear all data if not MSME or Buyer
                setUserName('');
                setUserKycStatus('');
                setCreditLineStatus(null);
                setHasActiveCreditLine(false);
            }
        };

        // This guard prevents the function from running before userId is available
        if (userId) {
            fetchUserSpecificData(userId);
            fetchNotifications();
        } else {
            // Clear all states if no user is logged in
            setUserName('');
            setUserKycStatus('');
            setCreditLineStatus(null);
            setHasActiveCreditLine(false);
            setNotifications([]);
        }
    }, [userId, isMsme, isBuyer]); // fetchNotifications is now a dependency

    const fetchNotifications = useCallback(async () => {
        if (!userId) {
            setNotifications([]);
            return;
        }
        try {
            const cacheBuster = new Date().getTime();
            const res = await fetch(`${config.apiUrl}/ops/invoiceFinancing/user-notifications?userId=${userId}&_=${cacheBuster}`);

            // This is the line that was fixed:
            const data = await res.json();

            if (res.ok && data.success && data.encryptedData) {
                const decryptedPayload = decryptData(data.encryptedData);

                if (decryptedPayload && Array.isArray(decryptedPayload.notifications)) {
                    const sortedNotifications = decryptedPayload.notifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                    setNotifications(sortedNotifications);
                } else {
                    console.error("Failed to decrypt notifications or format is incorrect.");
                    setNotifications([]);
                }
            } else {
                console.error("Error fetching notifications: Response not OK or data format incorrect", data);
                setNotifications([]);
            }
        } catch (err) {
            console.error("Error fetching user notifications", err);
            setNotifications([]);
        }
    }, [userId]);

    useEffect(() => {
        fetchNotifications();
    }, [fetchNotifications]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownOpen && profileDropdownRef.current && !profileDropdownRef.current.contains(event.target) && profileIconRef.current && !profileIconRef.current.contains(event.target)) {
                setDropdownOpen(false);
            }
            if (notificationDropdownOpen && notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target) && bellIconRef.current && !bellIconRef.current.contains(event.target)) {
                setNotificationDropdownOpen(false);
            }
            if (mobileNavOpen && mobileNavRef.current && !mobileNavRef.current.contains(event.target) && hamburgerIconRef.current && !hamburgerIconRef.current.contains(event.target)) {
                if (mobileTrayContentRef.current && !mobileTrayContentRef.current.contains(event.target)) {
                    setMobileNavOpen(false);
                } else if (!mobileTrayContentRef.current) {
                    setMobileNavOpen(false);
                }
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [dropdownOpen, notificationDropdownOpen, mobileNavOpen]);

    const handleLogout = () => {
        logout();
        setDropdownOpen(false);
        setNotificationDropdownOpen(false);
        setMobileNavOpen(false);
        history.push('/');
    };

    const isKycRoute = location.pathname.startsWith('/kyc');
    const isInvoiceContract = location.pathname.startsWith('/invoiceContract');
    const isCreditLineContract = location.pathname.startsWith('/creditLineContract');
    const isPrivacyPolicy = location.pathname === '/privacy-policy';
    const isTermsAndConditions = location.pathname === '/terms-and-conditions';
    const isLegalPage = isPrivacyPolicy || isTermsAndConditions;
    const showBackButton = isInvoiceContract || isCreditLineContract;

    const handleBack = () => {
        if (showBackButton) {
            // Determine where to go back based on user type
            history.push(isBuyer ? '/buyer-dashboard' : '/dashboard');
            setCurrentPage(isBuyer ? 'buyer-dashboard' : 'dashboard');
        }
    };

    const goToProfile = () => {
        if (isBuyer) {
            history.push('/buyer-dashboard'); // Or a specific buyer profile page if it exists
            setCurrentPage('buyer-dashboard'); // Set active page for buyer dashboard
        } else {
            history.push('/dashboard');
            setCurrentPage('settings'); // Set active page for MSME profile
        }
        setDropdownOpen(false);
        setMobileNavOpen(false);
    };

    const toggleNotificationDropdown = async () => {
        if (!notificationDropdownOpen) {
            await fetchNotifications();
        }
        setNotificationDropdownOpen(!notificationDropdownOpen);
        setDropdownOpen(false);
        setMobileNavOpen(false);
    };

    const handleNotificationClick = useCallback(async (notificationId) => {
        const notification = notifications.find(n => n._id === notificationId);
        if (!notification || notification.isRead) return;
        try {
            const res = await fetch(`${config.apiUrl}/ops/invoiceFinancing/user-notification-read`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ notificationId }),
            });
            const data = await res.json();
            if (res.ok && data.success) {
                setNotifications(prev => prev.map(n => n._id === notificationId ? { ...n, isRead: true } : n));
            } else {
                console.error("Failed to mark notification as read:", data);
            }
        } catch (err) {
            console.error("Error marking notification as read:", err);
        }
    }, [notifications, fetchNotifications]);

    const unreadCount = notifications.filter(n => !n.isRead).length;

    const formatDate = (dateString) => {
        try {
            const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
            return new Date(dateString).toLocaleDateString(undefined, options);
        } catch (e) {
            console.error("Error formatting date:", dateString, e);
            return "Invalid Date";
        }
    };

    // Define navigation links based on user type (for mobile menu)
    const getNavigationLinks = () => {
        const commonLinks = [
        ];

        if (isMsme) {
            // Determine if onboarding sections should be shown
            const showOnboardingOnly = userKycStatus === 'INITIATED' || userKycStatus === 'REINITIATED';

            if (showOnboardingOnly) {
                // If in INITIATED/REINITIATED status, ONLY show eligibility and onboarding
                return [
                    { name: 'Eligibility Check', icon: CheckCircleIcon, page: 'eligibility-check', path: '/eligibility-checker' },
                    { name: 'Onboarding', icon: UserCircleIcon, page: 'onboarding', path: '/kyc/personal-info' },
                    ...commonLinks // Include common links like Help and Support
                ];
            } else {
                // If not in INITIATED/REINITIATED status, show full dashboard items
                const msmeDashboardLinks = [
                    { name: 'Overview', icon: ChartBarIcon, page: 'dashboard', path: '/dashboard' },
                    // My Buyers and other active credit line related items
                    { name: 'My Buyers', icon: UserGroupIcon, page: 'my-buyers', path: '/dashboard', condition: hasActiveCreditLine },
                    { name: 'My Invoices', icon: ClipboardDocumentListIcon, page: 'my-invoices', path: '/dashboard' },
                    { name: 'Payments and Disburses', icon: BanknotesIcon, page: 'payments', path: '/dashboard' },
                ];
                // Filter out items that have an explicit 'false' condition (e.g., My Buyers if no active credit line)
                return [...msmeDashboardLinks.filter(link => link.condition === undefined || link.condition), ...commonLinks];
            }
        } else if (isBuyer) {
            const buyerLinks = [
                { name: 'Buyer Dashboard', icon: ChartBarIcon, page: 'buyer-dashboard', path: '/buyer-dashboard' },
                { name: 'Invoices for Approval', icon: ClipboardDocumentListIcon, page: 'invoices-approval', path: '/buyer-invoices' }, // Path changed!
            ];
            return [...buyerLinks, ...commonLinks];
        }
        return commonLinks;
    };

    const mainNavigationLinks = getNavigationLinks();


    const handleMobileNavigate = (page, pathOverride = null) => {
        if (page === 'onboarding') {
            history.push('/kyc/personal-info');
            setCurrentPage('onboarding');
        } else if (page === 'eligibility-check') {
            history.push('/eligibility-checker');
            setCurrentPage('eligibility-check');
        } else if (page === 'support') {
            history.push('/support');
            setCurrentPage('support');
        } else if (page === 'invoices-approval') { // Specific for Buyer's Invoices
            history.push('/buyer-invoices');
            setCurrentPage('invoices-approval');
        }
        else if (page === 'buyer-dashboard') { // Specific for Buyer's Dashboard
            history.push('/buyer-dashboard');
            setCurrentPage('buyer-dashboard');
        }
        else if (page === 'settings') { // Direct profile navigation
            goToProfile();
        }
        else {
            history.push(pathOverride || '/dashboard');
            setCurrentPage(page);
        }
        setMobileNavOpen(false);
    };


    const activeTabTextClass = 'text-[#edf5f7]';
    const activeTabBackgroundClass = 'bg-[#004141]';
    const inactiveTextClass = 'text-[#004141]';
    const inactiveHoverBgClass = 'hover:bg-[#d8e6e9]';
    const inactiveHoverTextClass = 'hover:text-[#003737]';

    const handleToggleUserType = () => {
        toggleUserType(); // This updates the userType in context and localStorage
        const newType = userType === 'msme' ? 'buyer' : 'msme';

        if (newType === 'buyer') {
            history.push('/buyer-dashboard');
            setCurrentPage('buyer-dashboard'); // Ensure context is updated for the new view
        } else { // newType is 'msme'
            // *** START: CHANGES HERE ***
            if (userKycStatus === 'INITIATED' || userKycStatus === 'REINITIATED') {
                history.push('/kyc/personal-info'); // Redirect to onboarding journey
                setCurrentPage('onboarding'); // Set current page for onboarding
            } else {
                history.push('/dashboard');
                setCurrentPage('dashboard'); // Ensure context is updated for the new view
            }
            // *** END: CHANGES HERE ***
        }
    };

    return (
        <header
            className={`${showBackButton ? 'bg-[#004141] py-4' : 'bg-white'} px-4 sm:px-6 flex items-center justify-between w-full min-h-[9vh] z-20 shadow-md relative`}
        >
            {/* Left section: Page Name, Hamburger (mobile) and Logo (mobile) / Back button */}
            <div className="flex items-center space-x-4">
                <div className="md:hidden flex items-center">
                    <button
                        ref={hamburgerIconRef}
                        onClick={() => setMobileNavOpen(!mobileNavOpen)}
                        className="p-2 rounded-md text-[#004141] hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#004141]"
                        aria-label="Open main menu"
                    >
                        <Bars3Icon className="h-7 w-7" />
                    </button>
                </div>
                {showBackButton ? (
                    <div className="flex items-center relative">
                        <button
                            onClick={handleBack}
                            className="bg-white text-[#004141] px-3 py-2 sm:px-6 sm:py-3 rounded-md hover:bg-gray-100 transition-colors font-medium text-sm sm:text-base"
                        >
                            Back to Dashboard
                        </button>
                    </div>
                ) : (
                    <div className="flex md:hidden items-center relative w-[70px] sm:w-[80px]">
                        <div className="flex items-center justify-center h-full">
                            <img
                                src={isLegalPage ? "/images/logo_footer.png" : require("../../images/logo.jpg")}
                                alt="Logo"
                                style={{ width: isLegalPage ? '100px' : '70px', height: isLegalPage ? 'auto' : '70px' }}
                                className="object-contain cursor-pointer"
                                onClick={() => {
                                    setMobileNavOpen(false);
                                    if (isBuyer) {
                                        history.push('/buyer-dashboard');
                                        setCurrentPage('buyer-dashboard');
                                    } else {
                                        history.push('/dashboard');
                                        setCurrentPage('dashboard');
                                    }
                                }}
                            />
                        </div>
                    </div>
                )}
                <div className="flex items-center hidden md:block">
                    <h1 className={`text-xl md:text-2xl lg:text-2xl font-bold ${showBackButton ? 'text-white' : 'text-[#004141]'} truncate`}>
                        {getPageDisplayName()}
                    </h1>
                </div>


            </div>

            {/* Right Side Icons & User Info */}
            <div className="relative flex items-center space-x-2 sm:space-x-4">
                {/* User Type Toggle */}
                {isDualRoleUser && (
                    <div
                        onClick={handleToggleUserType}
                        className={`flex items-center p-0.5 rounded-full bg-gray-200  cursor-pointer shadow-md transition-all duration-300 ease-in-out ${showBackButton ? 'border-2 border-white' : 'border-2 border-transparent'} hover:shadow-lg`}
                        title={`Switch to ${userType === 'msme' ? 'Buyer' : 'MSME'} View`}
                    >
                        <span
                            className={`text-center w-[70px] sm:w-[80px] px-2 py-1.5 text-xs sm:text-sm font-semibold rounded-full transition-all duration-300 ease-in-out
                                ${isMsme ? 'bg-[#004141] text-white' : 'text-gray-700'}`}
                        >
                            MSME
                        </span>
                        <span
                            className={`text-center w-[70px] sm:w-[80px] px-2 py-1.5 text-xs sm:text-sm font-semibold rounded-full transition-all duration-300 ease-in-out
                                ${isBuyer ? 'bg-[#004141] text-white' : 'text-gray-700 '}`}
                        >
                            Buyer
                        </span>
                    </div>
                )}

                <div className="relative" ref={bellIconRef}>
                    <BellIcon
                        className={`h-6 w-6 sm:h-7 sm:w-7 ${showBackButton ? 'text-white' : 'text-[#004141]'} cursor-pointer`}
                        onClick={toggleNotificationDropdown}
                    />
                    {unreadCount > 0 && (
                        <span className="absolute -top-1 -right-1 flex h-4 w-4 sm:h-5 sm:w-5 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white">
                            {unreadCount}
                        </span>
                    )}
                    {notificationDropdownOpen && (
                        <div
                            ref={notificationDropdownRef}
                            className="absolute right-0 mt-2 w-72 sm:w-80 max-h-96 overflow-y-auto bg-white rounded-md border border-gray-200 z-50"
                        >
                            <div className="p-3 border-b border-gray-200">
                                <h3 className="text-sm font-semibold text-gray-700">Notifications</h3>
                            </div>
                            {notifications.length > 0 ? (
                                <ul className="py-1 divide-y divide-gray-100">
                                    {notifications.map((notification) => (
                                        <li
                                            key={notification._id}
                                            className={`px-4 py-3 hover:bg-gray-100 cursor-pointer ${!notification.isRead ? 'bg-blue-50' : 'bg-white'}`}
                                            onClick={() => handleNotificationClick(notification._id)}
                                        >
                                            <p className={`text-sm ${!notification.isRead ? 'text-gray-800 font-semibold' : 'text-gray-600'}`}>{notification.title}</p>
                                            <p className={`text-xs mt-1 ${!notification.isRead ? 'text-gray-700' : 'text-gray-500'}`}>{notification.message}</p>
                                            <p className="text-xs text-gray-400 mt-1 text-right">{formatDate(notification.createdAt)}</p>
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-sm text-gray-500 p-4 text-center">No new notifications.</p>
                            )}
                        </div>
                    )}
                </div>

                <div className="relative">
                    <UserCircleIcon
                        ref={profileIconRef}
                        className={`h-7 w-7 sm:h-8 sm:w-8 ${showBackButton ? 'text-white' : 'text-[#004141]'} cursor-pointer`}
                        onClick={() => {
                            setDropdownOpen(!dropdownOpen);
                            setNotificationDropdownOpen(false);
                            setMobileNavOpen(false);
                        }}
                    />
                    {dropdownOpen && (
                        <div
                            ref={profileDropdownRef}
                            className="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md border border-gray-200 z-50"
                        >
                            <ul className="py-1">
                                {isMsme && (userKycStatus !== 'INITIATED' && userKycStatus !== 'REINITIATED') && (
                                    <li className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer" onClick={goToProfile}>Profile</li>

                                )}
                                <li className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer" onClick={handleLogout}>Logout</li>
                            </ul>
                        </div>
                    )}
                </div>
            </div>

            {/* Mobile Navigation Tray (Overlay) */}
            {mobileNavOpen && (
                <div
                    ref={mobileNavRef}
                    className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
                    onClick={() => setMobileNavOpen(false)}
                >
                    <div
                        ref={mobileTrayContentRef}
                        className="fixed top-0 left-0 h-full w-3/4 max-w-xs bg-[#edf5f7] shadow-xl p-4 flex flex-col transform transition-transform duration-300 ease-in-out z-50"
                        style={{ transform: mobileNavOpen ? 'translateX(0)' : 'translateX(-100%)' }}
                        onClick={e => e.stopPropagation()}
                    >
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-semibold text-[#004141]">Menu</h2>
                            <button
                                onClick={() => setMobileNavOpen(false)}
                                className="p-2 rounded-md text-[#004141] hover:bg-gray-300"
                                aria-label="Close menu"
                            >
                                <XMarkIcon className="h-6 w-6" />
                            </button>
                        </div>

                        {/* Mobile User Type Toggle */}
                        {isDualRoleUser && (
                            <div className="mb-4 flex justify-center">
                                <div
                                    className={`relative flex items-center p-1 rounded-full border border-[#004141] cursor-pointer transition-all duration-300 w-3/4`}
                                    onClick={handleToggleUserType}
                                >
                                    <span className={`text-sm font-medium px-3 py-1 rounded-full transition-all duration-300 ${isMsme ? 'bg-[#004141] text-white' : 'text-[#004141]'}`}>MSME</span>
                                    <span className={`text-sm font-medium px-3 py-1 rounded-full transition-all duration-300 ${isBuyer ? 'bg-[#004141] text-white' : 'text-[#004141]'}`}>Buyer</span>
                                    <div
                                        className={`absolute w-1/2 h-full bg-white rounded-full shadow-md transition-transform duration-300`}
                                        style={{ left: isBuyer ? '50%' : '0' }}
                                    ></div>
                                </div>
                            </div>
                        )}

                        {/* Main Navigation Items (flexible height) */}
                        <nav className="space-y-3 flex-grow overflow-y-auto custom-scrollbar">
                            {mainNavigationLinks.map((link) => {
                                if (link.condition === false) {
                                    return null;
                                }

                                return (
                                    <button
                                        key={link.name}
                                        className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === link.page
                                            ? `${activeTabBackgroundClass} ${activeTabTextClass}`
                                            : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
                                            }`}
                                        onClick={() => handleMobileNavigate(link.page, link.path)}
                                    >
                                        <link.icon className="h-6 w-6 mr-3" /> {link.name}
                                    </button>
                                );
                            })}
                        </nav>

                        {/* Divider */}
                        <div className="my-3 flex-shrink-0">
                            <hr className="border-t border-[#c0d0d3]" />
                        </div>

                        {/* Help and Support Section (fixed at bottom) */}
                        <nav className="flex-shrink-0 pb-4">
                            <button
                                className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === 'support'
                                    ? `${activeTabBackgroundClass} ${activeTabTextClass}`
                                    : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
                                    }`}
                                onClick={() => handleMobileNavigate('support', '/support')}
                            >
                                <ChatBubbleOvalLeftEllipsisIcon className="h-6 w-6 mr-3" /> Help and Support
                            </button>

                            {/* Hide Profile button if user is in INITIATED/REINITIATED status AND is an MSME */}
                            {isMsme && (userKycStatus !== 'INITIATED' && userKycStatus !== 'REINITIATED') && (
                                <button
                                    className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`}
                                    onClick={goToProfile}
                                >
                                    <UserCircleIcon className="h-6 w-6 mr-3" /> Profile
                                </button>
                            )}
                            {/* Always show Profile button for Buyers */}
                            {isBuyer && (
                                <button
                                    className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`}
                                    onClick={goToProfile}
                                >
                                    <UserCircleIcon className="h-6 w-6 mr-3" /> Profile
                                </button>
                            )}

                            <button
                                className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`}
                                onClick={handleLogout}
                            >
                                <ArrowLeftStartOnRectangleIcon className="h-6 w-6 mr-3" /> Logout
                            </button>
                        </nav>
                    </div>
                </div>
            )}
        </header>
    );
};

export default Header;